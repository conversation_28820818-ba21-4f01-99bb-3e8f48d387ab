import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import mkcert from "vite-plugin-mkcert";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), mkcert()],
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },
  server: {
    https: {
      // Using default mkcert-generated certificate
    },
    proxy: {
      // Proxy Eldorado API requests to bypass CORS
      '/api/eldorado': {
        target: 'https://www.eldorado.gg',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/eldorado/, '/api'),
        secure: true,
        // Enable cookie forwarding
        cookieDomainRewrite: {
          'www.eldorado.gg': 'localhost',
          '.eldorado.gg': 'localhost',
        },
        cookiePathRewrite: {
          '/': '/',
        },
        // Configure headers for proper cookie handling
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // Forward cookies from the original request
            if (req.headers.cookie) {
              proxyReq.setHeader('Cookie', req.headers.cookie);
            }
            // Set proper headers for Eldorado
            proxyReq.setHeader('Origin', 'https://www.eldorado.gg');
            proxyReq.setHeader('Referer', 'https://www.eldorado.gg/');
          });

          proxy.on('proxyRes', (proxyRes, req, res) => {
            // Handle Set-Cookie headers from the response
            const setCookieHeaders = proxyRes.headers['set-cookie'];
            if (setCookieHeaders) {
              // Rewrite domain and secure flags for local development
              const rewrittenCookies = setCookieHeaders.map(cookie => {
                return cookie
                  .replace(/Domain=\.?eldorado\.gg/gi, 'Domain=localhost')
                  .replace(/Secure;?/gi, '') // Remove Secure flag for localhost
                  .replace(/SameSite=None/gi, 'SameSite=Lax'); // Change SameSite for localhost
              });
              proxyRes.headers['set-cookie'] = rewrittenCookies;
            }
          });
        },
        headers: {
          'Origin': 'https://www.eldorado.gg',
          'Referer': 'https://www.eldorado.gg/',
        },
      },
    },
  },
});
